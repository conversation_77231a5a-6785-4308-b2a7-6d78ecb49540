import { post } from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'

import { DAppSiteInfo } from '@zeal/domains/DApp'
import { PredefinedNetwork } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { InitialUserOperation } from '@zeal/domains/UserOperation'

type RequestToCheck =
    | { type: 'rpc_request'; rpcRequest: EthSendTransaction }
    | { type: 'user_operation'; initialUserOperation: InitialUserOperation }

const requestToTransactionParam = (
    request: RequestToCheck
): EthSendTransaction['params'][0] => {
    switch (request.type) {
        case 'rpc_request':
            return request.rpcRequest.params[0]
        case 'user_operation':
            const { callData, entrypoint, sender } =
                request.initialUserOperation
            return {
                data: callData,
                from: entrypoint,
                to: sender,
            }

        default:
            return notReachable(request)
    }
}

const BLOCKAID_NETWORK: Record<PredefinedNetwork['name'], string> = {
    Ethereum: 'ethereum',
    BSC: 'bsc',
    Polygon: 'polygon',
    Avalanche: 'avalanche',
    Optimism: 'optimism',
    Arbitrum: 'arbitrum',
    Base: 'base',
    Gnosis: 'gnosis',

    // FIXME @resetko-zeal those are supported seems like, check
    PolygonZkevm: '-',
    Blast: '-',
    Aurora: '-',
    Linea: '-',
    OPBNB: '-',
    Celo: '-',
    Fantom: '-',
    zkSync: '-',
    Cronos: '-',
    Mantle: '-',
    Manta: '-',
}

/*



if (!blockaidScanningSupported(transaction.network)) return UnknownValidationResult
        try {
            val requestData = jsonObject()
                .put("from", transaction.from)
                .put("data", transaction.data)
                .put("value", transaction.value.toHex())
            if (transaction.to != null) {
                requestData.put("to", transaction.to!!)
            }
            val metadata = if (dApp != null) {
                jsonObject().put("domain", dApp.toString())
            } else {
                jsonObject().put("non_dapp", true)
            }
            val request = jsonObject()
                .putArray("options", listOf("validation"))
                .putPOJO("metadata", metadata)
                .putPOJO("data", requestData)
            val headers = defaultHeaders() + httpHeaders("X-Api-Key", config.apiKey)
            val network = fromCoreNetwork(transaction.network) ?: throw IllegalArgumentException("Unsupported network: ${transaction.network}")
            val result = httpClient.post(URI("/${network.value}/v0/validate/transaction"), headers, objectMapper.writeValueAsString(request)).body
            log.info("Blockaid transaction $transaction validation result: $result")
            return objectMapper.readValue(result, BlockaidTransactionScanningResult::class.java).validation
        } catch (e: Exception) {
            log.error("Blockaid transaction scanning check error occurred $transaction", e)
            return UnknownValidationResult
        }




*/

export const fetchTransactionsSafetyChecks = async ({
    requestToCheck,
    network,
    dApp,
    signal,
}: {
    requestToCheck: RequestToCheck
    network: PredefinedNetwork
    dApp: DAppSiteInfo | null
    signal?: AbortSignal
}): Promise<TransactionSafetyCheck[]> => {
    const trx = requestToTransactionParam(requestToCheck)

    const response = await post(
        `/proxy/ba/${BLOCKAID_NETWORK[network.name]}/v0/validate/transaction`,
        {
            body: {
                network_id: parseInt(network.hexChainId, 16),
                from: trx.from,
                to: trx.to,
                input: trx.data,
                save: false,
            },
        },
        signal
    )

    return []
}
