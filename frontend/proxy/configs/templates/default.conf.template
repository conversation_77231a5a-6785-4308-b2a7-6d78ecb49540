server {
    listen 8833;
    resolver "${NGINX_LOCAL_RESOLVERS}" valid=30s ipv6=off;

    include zeal/routes/biconomy.conf;
    include zeal/routes/block-analitica.conf;
    include zeal/routes/coingecko.conf;
    include zeal/routes/debank.conf;
    include zeal/routes/rpc.conf;
    include zeal/routes/sky-money.conf;
    include zeal/routes/tenderly.conf;
    include zeal/routes/unblock.conf;

    location / {
        return 404;
    }

    location /health {
        return 204;
    }
}
